"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Toggle } from "@workspace/ui/components/toggle";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON>it<PERSON>,
  SheetFooter,
} from "@workspace/ui/components/sheet";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@workspace/ui/components/dropdown-menu";
import {
  NestedCheckbox,
  NestedCheckboxItem,
  NestedCheckboxState,
} from "@workspace/ui/components/nested-checkbox";
import { Search } from "@workspace/ui/components/search";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { useRef, useState, useEffect } from "react";
import {
  ArrowDownNarrowWide,
  Edit,
  Flag,
  Flame,
  Heart,
  Info,
  LibraryBig,
  MoreVertical,
  Pen,
  Tag,
  User,
} from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@workspace/ui/components/pagination";
import Image from "next/image";
import { cn } from "@workspace/ui/lib/utils";
import { useActivitiesFiltered } from "../hooks/use-activities";
import { useActivityCategories } from "../hooks/use-categories";

interface SortProps {}

function Sort({}: SortProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="small">
          <ArrowDownNarrowWide className="size-6 stroke-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-44" align="end">
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>{"Aktivite İsmi"}</DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              <DropdownMenuRadioGroup value="asc">
                <DropdownMenuRadioItem value="asc">(A-Z)</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="desc">
                  (Z-A)
                </DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
        <DropdownMenuSeparator />
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>{"Senpai Sayısı"}</DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              <DropdownMenuRadioGroup value="asc">
                <DropdownMenuRadioItem value="asc">
                  {"Azalan"}
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="desc">
                  {"Artan"}
                </DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface CategorySheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  container: HTMLDivElement | null;
  activityCategories: NestedCheckboxItem[];
  categoryValues: NestedCheckboxState;
  onCategoryValuesChange: (values: NestedCheckboxState) => void;
}

function CategorySheet({
  open,
  onOpenChange,
  container,
  activityCategories,
  categoryValues,
  onCategoryValuesChange,
}: CategorySheetProps) {
  const handleClearCategories = () => {
    onCategoryValuesChange({});
  };

  // Check if any categories are selected
  const hasSelections = Object.values(categoryValues).some(
    (value) => value === true,
  );

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        overlay={false}
        container={container ?? undefined}
        side="right"
        className="w-[300px] h-[calc(100%-1rem)] top-1/2 -translate-y-1/2 absolute border-none px-2 bg-transparent shadow-none"
      >
        <SheetHeader className="pb-0">
          <SheetTitle>{"Kategoriler"}</SheetTitle>
        </SheetHeader>
        <div className="flex-1 overflow-auto p-4 scrollbar">
          <NestedCheckbox
            items={activityCategories}
            value={categoryValues}
            onValueChange={onCategoryValuesChange}
            className="space-y-2"
          />
        </div>
        <SheetFooter>
          {hasSelections && (
            <div className="flex">
              <Button
                size={"small"}
                className="px-12 mx-auto"
                onClick={handleClearCategories}
              >
                {"Temizle"}
              </Button>
            </div>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}

interface ActivityBrowserDialogProps {}

export function ActivityBrowserDialog({}: ActivityBrowserDialogProps) {
  const container = useRef<HTMLDivElement>(null);
  const [categoriesOpen, setCategoriesOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [showFavorites, setShowFavorites] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [moreOpen, setMoreOpen] = useState<string | null>(null);
  const [categoryValues, setCategoryValues] = useState<NestedCheckboxState>({});

  // Check if any categories have been selected/modified
  const hasCategorySelections = Object.values(categoryValues).some(
    (value) => value === true,
  );

  // Use query hooks for data
  const { data: activityCategories = [] } = useActivityCategories();
  const {
    data: currentActivities = [],
    totalPages = 1,
    isLoading: activitiesLoading,
    error: activitiesError,
  } = useActivitiesFiltered({
    searchValue,
    showFavorites,
    categoryFilters: categoryValues,
    page: currentPage,
    itemsPerPage: 15,
  });

  // Reset scroll position when page changes or search value changes
  useEffect(() => {
    const scrollableElement = document.getElementById(
      "activity-list-scrollable",
    );
    if (scrollableElement) {
      scrollableElement.scrollTop = 0;
    }
  }, [currentPage, searchValue]);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Here you would typically fetch new data or update the view
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages: (number | "ellipsis")[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first page
      pages.push(1);

      if (currentPage > 3) {
        pages.push("ellipsis");
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (currentPage < totalPages - 2) {
        pages.push("ellipsis");
      }

      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  return (
    <Dialog open>
      <DialogContent className="sm:max-w-[700px] overflow-hidden">
        <DialogHeader className="flex-row justify-between gap-4">
          <DialogTitle variant="stripe">{"AKTİVİTE SEÇ"}</DialogTitle>
          <div className="flex gap-2 shrink-0 pl-2 pr-8 translate-y-1">
            <Sort />
            <Toggle
              pressed={showFavorites}
              onPressedChange={setShowFavorites}
              size="small"
              aria-label="Toggle favorites"
              className="group"
            >
              <Heart className="size-6 group-data-[state=on]:fill-background" />
            </Toggle>
            <Button
              onClick={() => setCategoriesOpen((value) => !value)}
              size="small"
              variant={hasCategorySelections ? "primary" : "default"}
            >
              <LibraryBig
                className={cn(
                  "size-6",
                  hasCategorySelections && "fill-background",
                )}
              />
            </Button>
            <Search
              value={searchValue}
              onValueChange={setSearchValue}
              onSearch={(value) => {
                console.log("Searching for:", value);
                // Add your search logic here
              }}
              placeholder="Ara"
              size="small"
              align="end"
              className="w-60"
            />
          </div>
        </DialogHeader>

        <div
          id="activity-list"
          className="rounded-lg overflow-hidden border-2 relative h-96 mt-3 mx-7"
        >
          <div
            className="absolute inset-0 pointer-events-none"
            ref={container}
          ></div>
          <div
            id="activity-list-scrollable"
            className={cn(
              "absolute inset-0 scrollbar divide-y-2 divide-muted-foreground/50 overflow-y-auto overflow-x-hidden",
              categoriesOpen &&
                "scrollbar-track-transparent scrollbar-thumb-transparent",
              moreOpen && "pointer-events-none",
            )}
          >
            {activitiesLoading ? (
              // Loading skeleton
              Array.from({ length: 5 }).map((_, index) => (
                <div
                  key={`loading-${index}`}
                  className="flex w-full justify-between py-2 pr-0.5 gap-4 h-14 bg-muted odd:bg-background"
                >
                  <div className="flex pl-5 gap-5 items-center">
                    <Skeleton className="shrink-0 size-10 rounded-sm" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <div className="flex items-center gap-3 shrink-0">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              ))
            ) : activitiesError ? (
              <div className="flex items-center justify-center h-32 text-muted-foreground">
                Error loading activities
              </div>
            ) : currentActivities.length === 0 ? (
              <div className="flex items-center justify-center h-32 text-muted-foreground">
                No activities found
              </div>
            ) : (
              currentActivities.map((activity) => (
                <div
                  data-more={moreOpen === activity.title}
                  key={activity.title}
                  className={cn(
                    "group relative overflow-hidden flex w-full fake-text-stroke justify-between py-2 pr-0.5 gap-4 h-14 bg-muted odd:bg-background hover:bg-primary/50 focus-within:after:size-6 focus-within:after:bg-primary focus-within:after:absolute focus-within:after:-top-3 focus-within:after:-left-3 focus-within:after:rotate-45 data-[more=true]:bg-primary/50",
                  )}
                >
                  <button className="flex pl-5 gap-5 items-center text-base max-w-2/3 grow outline-none">
                    <Image
                      src={`/${activity.icon}`}
                      alt={activity.title}
                      width={50}
                      height={50}
                      className="shrink-0 size-10 rounded-sm ring-2 ring-foreground border-2 border-background"
                    />
                    <p className="truncate">{activity.title}</p>
                  </button>

                  <div className="flex items-center gap-3 shrink-0 max-w-2/3">
                    {activity.hot && (
                      <div className="bg-primary/50 py-1.5 pl-3 pr-5 flex justify-between text-xs items-center gap-1.5 text-primary-foreground text-shadow-none rounded-full">
                        <Flame className="shrink-0 size-5 stroke-primary-foreground" />
                        <p>{"POP"}</p>
                      </div>
                    )}
                    <div className="bg-foreground/20 py-1.5 pl-2.5 pr-5 flex justify-between text-sm gap-1.5 w-22 text-foreground text-shadow-none rounded-full">
                      <User className="shrink-0 size-5 stroke-foreground" />
                      <p>{activity["senpai-count"]}</p>
                    </div>
                    <div className="flex relative gap-2">
                      <button
                        data-fav={activity.fav}
                        className="shrink-0 hidden group-hover:block group-focus-within:block group-data-[more=true]:block data-[fav=true]:block group"
                      >
                        <Heart className="hover:fill-foreground group-data-[fav=true]:fill-foreground group-data-[fav=true]:stroke-0" />
                      </button>
                      <button
                        onClick={() =>
                          setMoreOpen(
                            moreOpen === activity.title ? null : activity.title,
                          )
                        }
                        className={cn(
                          "shrink-0 hidden group-hover:block group-focus-within:block",
                          moreOpen === activity.title && "block",
                        )}
                      >
                        <MoreVertical />
                      </button>
                      <DropdownMenu
                        open={moreOpen === activity.title}
                        onOpenChange={(open) =>
                          setMoreOpen(open ? activity.title : null)
                        }
                      >
                        <DropdownMenuTrigger asChild>
                          <div className="h-full"></div>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          sideOffset={35}
                          className="w-36 mr-1"
                        >
                          <DropdownMenuItem>
                            <Flag className="mr-1 stroke-foreground size-5 stroke-2" />
                            {"Bildir"}
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Pen className="mr-1 stroke-foreground size-5 stroke-2" />
                            {"Düzenle"}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <CategorySheet
          open={categoriesOpen}
          onOpenChange={setCategoriesOpen}
          container={container.current}
          activityCategories={activityCategories}
          categoryValues={categoryValues}
          onCategoryValuesChange={setCategoryValues}
        />
        <DialogFooter>
          <Pagination>
            <PaginationContent className="flex justify-between gap-2 w-full px-8">
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePrevious();
                  }}
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                />
              </PaginationItem>

              <div className="flex gap-2">
                {getPageNumbers().map((page, index) => (
                  <PaginationItem key={index}>
                    {page === "ellipsis" ? (
                      <PaginationEllipsis />
                    ) : (
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handlePageChange(page);
                        }}
                        isActive={currentPage === page}
                      >
                        {page}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}
              </div>

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleNext();
                  }}
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
