import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useActivityCategories } from '../use-categories';
import React from 'react';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    React.createElement(QueryClientProvider, { client: queryClient }, children)
  );
};

describe('useActivityCategories', () => {
  it('should fetch activity categories successfully', async () => {
    const { result } = renderHook(() => useActivityCategories(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toBeDefined();
    expect(result.current.data).toHaveLength(3);
    
    // Check the structure of the first category
    const firstCategory = result.current.data?.[0];
    expect(firstCategory?.id).toBe('oyun');
    expect(firstCategory?.label).toBe('Oyun');
    expect(firstCategory?.children).toBeDefined();
    expect(firstCategory?.children).toHaveLength(4);
  });

  it('should have correct nested structure', async () => {
    const { result } = renderHook(() => useActivityCategories(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    const oyunCategory = result.current.data?.find(cat => cat.id === 'oyun');
    const rekabetciCategory = oyunCategory?.children?.find(cat => cat.id === 'rekabetci');
    
    expect(rekabetciCategory).toBeDefined();
    expect(rekabetciCategory?.children).toHaveLength(3);
    expect(rekabetciCategory?.children?.[0].id).toBe('fps');
  });
});
