{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@turbo/gen": "^2.5.5", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}, "exports": {"./styles/*": "./src/styles/*.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}